#!/bin/bash

# Queue Test Runner Script
# This script sets up the environment and runs the concurrent API test

echo "=========================================="
echo "Audio Processing Queue Test Runner"
echo "=========================================="

# Check if audio file exists
AUDIO_FILE="Record (online-voice-recorder.com) (1).mp3"
if [ ! -f "$AUDIO_FILE" ]; then
    echo "ERROR: Audio file '$AUDIO_FILE' not found!"
    echo "Please ensure the audio file exists in the current directory."
    exit 1
fi

echo "✓ Audio file found: $AUDIO_FILE"

# Set test mode environment variable
export AUDIO_ANALYSIS_TEST_MODE=true
echo "✓ Test mode enabled (will use 10-second sleep instead of actual processing)"

# Check if the API server is running
echo "Checking if API server is running..."
if curl -s -f "http://127.0.0.1:8000/health" > /dev/null 2>&1; then
    echo "✓ API server is running"
else
    echo "WARNING: API server may not be running at http://127.0.0.1:8000"
    echo "Please ensure the FastAPI server is started before running this test."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check if RabbitMQ is running (optional check)
echo "Checking RabbitMQ status..."
if command -v rabbitmqctl &> /dev/null; then
    if rabbitmqctl status > /dev/null 2>&1; then
        echo "✓ RabbitMQ is running"
    else
        echo "WARNING: RabbitMQ may not be running"
    fi
else
    echo "INFO: rabbitmqctl not found, skipping RabbitMQ check"
fi

# Run the Python test script
echo ""
echo "Starting concurrent test with 100 requests..."
echo "This will test the queue system's ability to handle concurrent load."
echo ""

python3 test_concurrent_audio_processing.py

# Check exit code
if [ $? -eq 0 ]; then
    echo ""
    echo "✓ Test completed successfully!"
    echo "Check the results above and the generated JSON file for detailed analysis."
else
    echo ""
    echo "✗ Test failed or encountered errors."
    echo "Check the error messages above for troubleshooting."
fi

echo ""
echo "Test Summary:"
echo "- 100 concurrent requests were sent to the audio-transcribe-analysis API"
echo "- Each request used a 10-second sleep instead of actual processing"
echo "- This tests the queue system's concurrency handling and limits"
echo "- Check queue settings in queue_management/async_rabbitmq.py:"
echo "  * max_concurrent_jobs = 20"
echo "  * max_queue_length = 50"
echo "  * prefetch_count = 20"
