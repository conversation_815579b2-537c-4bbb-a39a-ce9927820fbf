# Audio Processing Queue Test

This test suite is designed to test the queue system's ability to handle 100 concurrent requests to the `audio-transcribe-analysis` API endpoint without actually processing the audio files (to save costs).

## What This Test Does

1. **Modifies the audio processing logic** to use a 10-second sleep instead of actual Gemini API calls
2. **Sends 100 concurrent requests** to the audio-transcribe-analysis endpoint
3. **Tests the queue system** including:
   - RabbitMQ queue handling
   - Concurrent job processing limits
   - Queue overflow behavior
   - Response times and success rates

## Queue Configuration Being Tested

The test validates these queue settings from `queue_management/async_rabbitmq.py`:

```python
max_concurrent_jobs = 20      # Maximum concurrent processing
max_queue_length = 50         # Maximum queue size
prefetch_count = 20          # RabbitMQ prefetch limit
```

## Files Created

1. **`test_concurrent_audio_processing.py`** - Main test script
2. **`run_queue_test.sh`** - Shell script to run the test
3. **`check_test_requirements.py`** - Requirements checker
4. **Modified `app/v1/processes/audio_analysis/__init__.py`** - Added test mode

## Prerequisites

1. **Audio file**: Place `Record (online-voice-recorder.com) (1).mp3` in the project root
2. **API server**: FastAPI server running on `http://127.0.0.1:8000`
3. **RabbitMQ**: RabbitMQ server running (for queue functionality)
4. **Python packages**: `aiohttp` (install with `pip install aiohttp`)

## How to Run the Test

### Option 1: Quick Start (Recommended)
```bash
# Check requirements first
python3 check_test_requirements.py

# Run the test
./run_queue_test.sh
```

### Option 2: Manual Run
```bash
# Set test mode
export AUDIO_ANALYSIS_TEST_MODE=true

# Run the test
python3 test_concurrent_audio_processing.py
```

## Test Mode

When `AUDIO_ANALYSIS_TEST_MODE=true` is set, the audio processing will:
- ✅ Accept and save the uploaded audio file
- ✅ Sleep for 10 seconds (simulating processing time)
- ✅ Return mock transcription and analysis results
- ❌ NOT call the Gemini API (saves money)
- ❌ NOT perform actual audio processing

## Expected Results

### Successful Test Scenario
- **Success Rate**: 80-100% (depending on system resources)
- **Queue Handling**: Should handle up to 50 requests in queue
- **Concurrent Processing**: Maximum 20 jobs processing simultaneously
- **Response Times**: ~10-15 seconds per request (10s sleep + overhead)

### What to Look For
1. **Queue Overflow**: Requests beyond queue limit should be rejected
2. **Concurrency Limits**: Only 20 jobs should process simultaneously
3. **Response Distribution**: Check status codes and error patterns
4. **Timing**: Verify requests complete in reasonable time

## Output Files

The test generates:
- **Console output**: Real-time test progress and summary
- **JSON results file**: `concurrent_test_results_<timestamp>.json` with detailed analysis

## Troubleshooting

### Common Issues

1. **Audio file not found**
   ```
   ERROR: Audio file 'Record (online-voice-recorder.com) (1).mp3' not found!
   ```
   **Solution**: Place the audio file in the project root directory

2. **API server not running**
   ```
   WARNING: API server may not be running at http://127.0.0.1:8000
   ```
   **Solution**: Start the FastAPI server first

3. **Missing dependencies**
   ```
   ModuleNotFoundError: No module named 'aiohttp'
   ```
   **Solution**: Install with `pip install aiohttp`

4. **All requests failing**
   - Check if test mode is enabled: `echo $AUDIO_ANALYSIS_TEST_MODE`
   - Verify API authentication token is valid
   - Check server logs for errors

### Queue-Specific Issues

1. **Queue overflow errors**: Expected when sending more than 50 requests
2. **Timeout errors**: May occur if system is overloaded
3. **Connection errors**: Check RabbitMQ is running

## Interpreting Results

### Key Metrics
- **Success Rate**: Should be high (>80%) for healthy system
- **Average Duration**: Should be ~10-12 seconds in test mode
- **Queue Handling**: Should show "PASSED" if queue works correctly
- **Status Codes**: Mostly 200s, some 429s (rate limiting) are normal

### Queue Performance Indicators
- **Concurrent Capacity**: Tests 100 requests against 20 concurrent limit
- **Timeout Rate**: Should be low (<10%) for healthy system
- **Error Distribution**: Check for patterns in failures

## Reverting Changes

To disable test mode and return to normal processing:
```bash
unset AUDIO_ANALYSIS_TEST_MODE
# or
export AUDIO_ANALYSIS_TEST_MODE=false
```

The audio processing will automatically return to normal Gemini API calls.

## Next Steps

After running the test:
1. **Analyze the results** to understand queue behavior
2. **Adjust queue settings** if needed in `queue_management/async_rabbitmq.py`
3. **Test with different concurrency levels** by modifying `CONCURRENT_REQUESTS`
4. **Monitor system resources** during high load scenarios
