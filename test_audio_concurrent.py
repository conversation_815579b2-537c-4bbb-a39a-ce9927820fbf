#!/usr/bin/env python3
"""
Concurrent Audio Analysis API Test Script

This script tests the audio-transcribe-analysis API endpoint with 50 concurrent requests
using mock processing (10-second sleep instead of actual LLM calls).

Usage:
    python test_audio_concurrent.py

Environment Variables:
    AUDIO_ANALYSIS_TEST_MODE=true (enables mock mode)
"""

import asyncio
import aiohttp
import time
import json
import os
from pathlib import Path
from typing import List, Dict, Any
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AudioConcurrentTester:
    def __init__(self, base_url: str = "http://127.0.0.1:8300", token: str = None):
        self.base_url = base_url
        self.token = token
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
            
    def get_headers(self) -> Dict[str, str]:
        """Get request headers with authorization"""
        headers = {
            'accept': 'application/json'
        }
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'
        return headers
        
    async def create_test_audio_file(self) -> str:
        """Create a small test audio file for testing"""
        # Create a minimal MP3 file (just headers, won't play but will be accepted)
        test_audio_path = "test_audio.mp3"
        
        # Create a minimal MP3 file with ID3 header
        mp3_header = b'ID3\x03\x00\x00\x00\x00\x00\x00'
        mp3_frame = b'\xff\xfb\x90\x00' + b'\x00' * 100  # Minimal MP3 frame
        
        with open(test_audio_path, 'wb') as f:
            f.write(mp3_header + mp3_frame)
            
        return test_audio_path
        
    async def make_single_request(self, request_id: int, audio_file_path: str) -> Dict[str, Any]:
        """Make a single API request"""
        start_time = time.time()
        
        try:
            url = f"{self.base_url}/v1/processes/audio-transcribe-analysis/run"
            params = {
                'wait_for_completion': 'true',
                'timeout': '300'
            }
            
            # Prepare multipart form data
            with open(audio_file_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('files', f, filename='test_audio.mp3', content_type='audio/mpeg')
                
                async with self.session.post(
                    url,
                    headers=self.get_headers(),
                    params=params,
                    data=data
                ) as response:
                    duration = time.time() - start_time
                    response_text = await response.text()
                    
                    result = {
                        "request_id": request_id,
                        "success": response.status == 200,
                        "status_code": response.status,
                        "duration": duration,
                        "response_size": len(response_text)
                    }
                    
                    if response.status == 200:
                        try:
                            response_json = json.loads(response_text)
                            result["job_id"] = response_json.get("job_id")
                            result["status"] = response_json.get("status")
                        except json.JSONDecodeError:
                            result["error"] = "Invalid JSON response"
                    else:
                        result["error"] = f"HTTP {response.status}: {response_text[:100]}"
                        
                    return result
                    
        except Exception as e:
            duration = time.time() - start_time
            return {
                "request_id": request_id,
                "success": False,
                "status_code": 0,
                "duration": duration,
                "response_size": 0,
                "error": str(e)
            }
            
    async def run_concurrent_test(self, num_requests: int = 50) -> Dict[str, Any]:
        """Run concurrent test with specified number of requests"""
        logger.info(f"Starting concurrent test with {num_requests} requests")
        
        # Create test audio file
        audio_file_path = await self.create_test_audio_file()
        
        try:
            # Record start time
            test_start_time = time.time()
            
            # Create tasks for concurrent requests
            tasks = [
                self.make_single_request(i + 1, audio_file_path)
                for i in range(num_requests)
            ]
            
            # Execute all requests concurrently
            logger.info("Executing concurrent requests...")
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Calculate total test duration
            total_duration = time.time() - test_start_time
            
            # Process results
            successful_requests = 0
            failed_requests = 0
            total_request_duration = 0
            min_duration = float('inf')
            max_duration = 0
            status_codes = {}
            errors = {}
            
            for result in results:
                if isinstance(result, Exception):
                    failed_requests += 1
                    error_msg = str(result)
                    errors[error_msg] = errors.get(error_msg, 0) + 1
                else:
                    if result["success"]:
                        successful_requests += 1
                    else:
                        failed_requests += 1
                        
                    duration = result["duration"]
                    total_request_duration += duration
                    min_duration = min(min_duration, duration)
                    max_duration = max(max_duration, duration)
                    
                    status_code = result["status_code"]
                    status_codes[status_code] = status_codes.get(status_code, 0) + 1
                    
                    if "error" in result:
                        error_msg = result["error"]
                        errors[error_msg] = errors.get(error_msg, 0) + 1
            
            # Calculate statistics
            success_rate = (successful_requests / num_requests) * 100
            avg_duration = total_request_duration / num_requests if num_requests > 0 else 0
            requests_per_second = num_requests / total_duration if total_duration > 0 else 0
            
            # Compile analysis
            analysis = {
                "test_summary": {
                    "total_requests": num_requests,
                    "successful_requests": successful_requests,
                    "failed_requests": failed_requests,
                    "success_rate": success_rate,
                    "total_test_duration": total_duration
                },
                "timing_stats": {
                    "average_request_duration": avg_duration,
                    "min_request_duration": min_duration if min_duration != float('inf') else 0,
                    "max_request_duration": max_duration,
                    "requests_per_second": requests_per_second
                },
                "status_code_distribution": status_codes,
                "error_analysis": errors,
                "queue_performance": {
                    "concurrent_capacity": num_requests,
                    "queue_handling": "PASSED" if success_rate > 80 else "FAILED",
                    "timeout_rate": 0.0  # We don't track timeouts separately in this test
                }
            }
            
            return {
                "analysis": analysis,
                "detailed_results": [r for r in results if not isinstance(r, Exception)]
            }
            
        finally:
            # Clean up test file
            if os.path.exists(audio_file_path):
                os.remove(audio_file_path)
                
    def save_results(self, results: Dict[str, Any], filename: str = None):
        """Save test results to JSON file"""
        if filename is None:
            timestamp = int(time.time())
            filename = f"audio_concurrent_test_results_{timestamp}.json"
            
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
            
        logger.info(f"Results saved to {filename}")
        return filename

async def main():
    """Main function to run the concurrent test"""
    # Configuration
    BASE_URL = "http://127.0.0.1:8300"
    TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjgyYzJiNGQwZWIwOTkxN2U1ZDkzOTU0IiwiZXhwIjoxNzU0NDcyMTU4fQ.OpBLLih8r6f8A9N7PuvGQMvSyrLnU6H-mtzkpWEZ2NE"
    NUM_REQUESTS = 50
    
    # Set test mode environment variable
    os.environ["AUDIO_ANALYSIS_TEST_MODE"] = "true"
    logger.info("Test mode enabled - will use 10-second sleep instead of LLM processing")
    
    async with AudioConcurrentTester(BASE_URL, TOKEN) as tester:
        logger.info(f"Starting concurrent test with {NUM_REQUESTS} requests")
        logger.info(f"Target URL: {BASE_URL}/v1/processes/audio-transcribe-analysis/run")
        
        # Run the test
        results = await tester.run_concurrent_test(NUM_REQUESTS)
        
        # Save results
        filename = tester.save_results(results)
        
        # Print summary
        analysis = results["analysis"]
        print("\n" + "="*60)
        print("CONCURRENT AUDIO ANALYSIS TEST RESULTS")
        print("="*60)
        print(f"Total Requests: {analysis['test_summary']['total_requests']}")
        print(f"Successful: {analysis['test_summary']['successful_requests']}")
        print(f"Failed: {analysis['test_summary']['failed_requests']}")
        print(f"Success Rate: {analysis['test_summary']['success_rate']:.1f}%")
        print(f"Total Duration: {analysis['test_summary']['total_test_duration']:.2f}s")
        print(f"Avg Request Duration: {analysis['timing_stats']['average_request_duration']:.2f}s")
        print(f"Requests/Second: {analysis['timing_stats']['requests_per_second']:.2f}")
        print(f"Queue Performance: {analysis['queue_performance']['queue_handling']}")
        print(f"\nResults saved to: {filename}")
        print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
