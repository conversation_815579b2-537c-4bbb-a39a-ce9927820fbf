{"analysis": {"test_summary": {"total_requests": 50, "successful_requests": 22, "failed_requests": 28, "success_rate": 44.0, "total_test_duration": 6.3612542152404785}, "timing_stats": {"average_request_duration": 5.0253327941894534, "min_request_duration": 4.001857042312622, "max_request_duration": 6.355216026306152, "requests_per_second": 7.860085182605742}, "status_code_distribution": {"200": 22, "500": 28}, "error_analysis": {"HTTP 500: Internal Server Error": 28}, "queue_performance": {"concurrent_capacity": 50, "queue_handling": "FAILED", "timeout_rate": 0.0}}, "detailed_results": [{"request_id": 1, "success": true, "status_code": 200, "duration": 6.342782974243164, "response_size": 100, "job_id": "6891dba0f630a5cad066326d", "status": "in-progress"}, {"request_id": 2, "success": true, "status_code": 200, "duration": 6.326358079910278, "response_size": 100, "job_id": "6891dba0f630a5cad0663270", "status": "in-progress"}, {"request_id": 3, "success": true, "status_code": 200, "duration": 6.341819763183594, "response_size": 100, "job_id": "6891dba0f630a5cad0663267", "status": "in-progress"}, {"request_id": 4, "success": true, "status_code": 200, "duration": 6.341302871704102, "response_size": 100, "job_id": "6891dba0f630a5cad0663272", "status": "in-progress"}, {"request_id": 5, "success": true, "status_code": 200, "duration": 6.253801107406616, "response_size": 100, "job_id": "6891dba0f630a5cad0663264", "status": "in-progress"}, {"request_id": 6, "success": true, "status_code": 200, "duration": 6.339617967605591, "response_size": 100, "job_id": "6891dba0f630a5cad0663262", "status": "in-progress"}, {"request_id": 7, "success": false, "status_code": 500, "duration": 4.008591890335083, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 8, "success": false, "status_code": 500, "duration": 4.008404016494751, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 9, "success": false, "status_code": 500, "duration": 4.0080671310424805, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 10, "success": false, "status_code": 500, "duration": 4.0077738761901855, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 11, "success": false, "status_code": 500, "duration": 4.008265256881714, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 12, "success": false, "status_code": 500, "duration": 4.007327079772949, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 13, "success": false, "status_code": 500, "duration": 4.007077932357788, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 14, "success": false, "status_code": 500, "duration": 4.00665807723999, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 15, "success": false, "status_code": 500, "duration": 4.006402015686035, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 16, "success": false, "status_code": 500, "duration": 4.007668972015381, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 17, "success": false, "status_code": 500, "duration": 4.007319927215576, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 18, "success": false, "status_code": 500, "duration": 4.007097244262695, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 19, "success": false, "status_code": 500, "duration": 4.006168842315674, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 20, "success": false, "status_code": 500, "duration": 4.005867004394531, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 21, "success": false, "status_code": 500, "duration": 4.0056681632995605, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 22, "success": false, "status_code": 500, "duration": 4.005232810974121, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 23, "success": false, "status_code": 500, "duration": 4.004934072494507, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 24, "success": false, "status_code": 500, "duration": 4.004759073257446, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 25, "success": false, "status_code": 500, "duration": 4.004591226577759, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 26, "success": false, "status_code": 500, "duration": 4.004426002502441, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 27, "success": false, "status_code": 500, "duration": 4.004197120666504, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 28, "success": false, "status_code": 500, "duration": 4.004009246826172, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 29, "success": false, "status_code": 500, "duration": 4.003832101821899, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 30, "success": false, "status_code": 500, "duration": 4.003471851348877, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 31, "success": false, "status_code": 500, "duration": 4.003306150436401, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 32, "success": false, "status_code": 500, "duration": 4.003099203109741, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 33, "success": false, "status_code": 500, "duration": 4.002427816390991, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 34, "success": false, "status_code": 500, "duration": 4.001857042312622, "response_size": 21, "error": "HTTP 500: Internal Server Error"}, {"request_id": 35, "success": true, "status_code": 200, "duration": 6.3363938331604, "response_size": 100, "job_id": "6891dba0f630a5cad066326a", "status": "in-progress"}, {"request_id": 36, "success": true, "status_code": 200, "duration": 6.3483898639678955, "response_size": 100, "job_id": "6891dba0f630a5cad0663260", "status": "in-progress"}, {"request_id": 37, "success": true, "status_code": 200, "duration": 6.311448097229004, "response_size": 100, "job_id": "6891dba0f630a5cad066325f", "status": "in-progress"}, {"request_id": 38, "success": true, "status_code": 200, "duration": 6.32148003578186, "response_size": 100, "job_id": "6891dba0f630a5cad0663261", "status": "in-progress"}, {"request_id": 39, "success": true, "status_code": 200, "duration": 6.3165998458862305, "response_size": 100, "job_id": "6891dba0f630a5cad066326e", "status": "in-progress"}, {"request_id": 40, "success": true, "status_code": 200, "duration": 6.227082014083862, "response_size": 100, "job_id": "6891dba0f630a5cad0663265", "status": "in-progress"}, {"request_id": 41, "success": true, "status_code": 200, "duration": 6.316802024841309, "response_size": 100, "job_id": "6891dba0f630a5cad0663263", "status": "in-progress"}, {"request_id": 42, "success": true, "status_code": 200, "duration": 6.310388088226318, "response_size": 100, "job_id": "6891dba0f630a5cad0663266", "status": "in-progress"}, {"request_id": 43, "success": true, "status_code": 200, "duration": 6.315955877304077, "response_size": 100, "job_id": "6891dba0f630a5cad066326c", "status": "in-progress"}, {"request_id": 44, "success": true, "status_code": 200, "duration": 6.321624755859375, "response_size": 100, "job_id": "6891dba0f630a5cad0663269", "status": "in-progress"}, {"request_id": 45, "success": true, "status_code": 200, "duration": 6.355216026306152, "response_size": 100, "job_id": "6891dba0f630a5cad0663271", "status": "in-progress"}, {"request_id": 46, "success": true, "status_code": 200, "duration": 6.3370890617370605, "response_size": 100, "job_id": "6891dba0f630a5cad0663273", "status": "in-progress"}, {"request_id": 47, "success": true, "status_code": 200, "duration": 6.354061126708984, "response_size": 100, "job_id": "6891dba0f630a5cad0663274", "status": "in-progress"}, {"request_id": 48, "success": true, "status_code": 200, "duration": 6.316461086273193, "response_size": 100, "job_id": "6891dba0f630a5cad066326b", "status": "in-progress"}, {"request_id": 49, "success": true, "status_code": 200, "duration": 6.337023019790649, "response_size": 100, "job_id": "6891dba0f630a5cad0663268", "status": "in-progress"}, {"request_id": 50, "success": true, "status_code": 200, "duration": 6.3364410400390625, "response_size": 100, "job_id": "6891dba0f630a5cad066326f", "status": "in-progress"}]}