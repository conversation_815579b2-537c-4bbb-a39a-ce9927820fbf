#!/usr/bin/env python3
"""
Check if all requirements for the concurrent test are met.
"""

import sys
import os
import subprocess
import importlib

def check_python_packages():
    """Check if required Python packages are installed."""
    required_packages = ['aiohttp', 'asyncio']
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✓ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} is NOT installed")
    
    return missing_packages

def check_audio_file():
    """Check if the audio file exists."""
    audio_file = "Record (online-voice-recorder.com) (1).mp3"
    if os.path.exists(audio_file):
        print(f"✓ Audio file found: {audio_file}")
        file_size = os.path.getsize(audio_file)
        print(f"  File size: {file_size} bytes ({file_size/1024/1024:.2f} MB)")
        return True
    else:
        print(f"✗ Audio file NOT found: {audio_file}")
        return False

def check_api_server():
    """Check if the API server is accessible."""
    try:
        import urllib.request
        import urllib.error
        
        # Try to access the API
        req = urllib.request.Request("http://127.0.0.1:8000/docs")
        with urllib.request.urlopen(req, timeout=5) as response:
            if response.status == 200:
                print("✓ API server is accessible at http://127.0.0.1:8000")
                return True
    except urllib.error.URLError:
        print("✗ API server is NOT accessible at http://127.0.0.1:8000")
        print("  Please start the FastAPI server first")
    except Exception as e:
        print(f"✗ Error checking API server: {e}")
    
    return False

def main():
    print("Concurrent Test Requirements Check")
    print("=" * 50)
    
    all_good = True
    
    # Check Python version
    python_version = sys.version_info
    if python_version >= (3, 7):
        print(f"✓ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"✗ Python version too old: {python_version.major}.{python_version.minor}.{python_version.micro}")
        print("  Python 3.7+ is required")
        all_good = False
    
    print()
    
    # Check Python packages
    print("Checking Python packages:")
    missing_packages = check_python_packages()
    if missing_packages:
        print(f"\nTo install missing packages, run:")
        print(f"pip install {' '.join(missing_packages)}")
        all_good = False
    
    print()
    
    # Check audio file
    print("Checking audio file:")
    if not check_audio_file():
        print("  Please ensure the audio file is in the current directory")
        all_good = False
    
    print()
    
    # Check API server
    print("Checking API server:")
    if not check_api_server():
        print("  Please start the FastAPI server before running the test")
        all_good = False
    
    print()
    print("=" * 50)
    
    if all_good:
        print("✓ All requirements are met! You can run the test.")
        print("\nTo run the test:")
        print("  ./run_queue_test.sh")
        print("  or")
        print("  python3 test_concurrent_audio_processing.py")
    else:
        print("✗ Some requirements are not met. Please fix the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
