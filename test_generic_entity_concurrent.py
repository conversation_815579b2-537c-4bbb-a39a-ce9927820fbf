#!/usr/bin/env python3
"""
Concurrent Generic Entity Extraction API Test Script

This script tests the generic-entity-extraction API endpoint with 50 concurrent requests
using the sentiment.json file.

Usage:
    python test_generic_entity_concurrent.py
"""

import asyncio
import aiohttp
import time
import json
import os
from typing import List, Dict, Any
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GenericEntityConcurrentTester:
    def __init__(self, base_url: str = "http://127.0.0.1:8000", token: str = None):
        self.base_url = base_url
        self.token = token
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        timeout = aiohttp.ClientTimeout(total=600)  # 10 minute timeout
        self.session = aiohttp.ClientSession(timeout=timeout)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
            
    def get_headers(self) -> Dict[str, str]:
        """Get request headers with authorization"""
        headers = {
            'accept': 'application/json'
        }
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'
        return headers
        
    async def make_single_request(self, request_id: int, json_file_path: str) -> Dict[str, Any]:
        """Make a single API request"""
        start_time = time.time()
        
        try:
            url = f"{self.base_url}/v1/processes/generic-entity-extraction/run"
            params = {
                'wait_for_completion': 'false',  # Don't wait for completion
                'timeout': '300'
            }
            
            # Prepare multipart form data
            with open(json_file_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('files', f, filename=f'sentiment_{request_id}.json', content_type='application/json')
                
                async with self.session.post(
                    url,
                    headers=self.get_headers(),
                    params=params,
                    data=data
                ) as response:
                    duration = time.time() - start_time
                    response_text = await response.text()
                    
                    result = {
                        "request_id": request_id,
                        "success": response.status == 200,
                        "status_code": response.status,
                        "duration": duration,
                        "response_size": len(response_text)
                    }
                    
                    if response.status == 200:
                        try:
                            response_json = json.loads(response_text)
                            result["job_id"] = response_json.get("job_id")
                            result["status"] = response_json.get("status")
                            logger.info(f"✅ Request {request_id}: {duration:.2f}s - Job {result['job_id']}")
                        except json.JSONDecodeError:
                            result["error"] = "Invalid JSON response"
                            logger.error(f"❌ Request {request_id}: Invalid JSON")
                    else:
                        result["error"] = f"HTTP {response.status}: {response_text[:100]}"
                        logger.error(f"❌ Request {request_id}: {response.status} - {response_text[:50]}")
                        
                    return result
                    
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"❌ Request {request_id}: Exception - {str(e)}")
            return {
                "request_id": request_id,
                "success": False,
                "status_code": 0,
                "duration": duration,
                "response_size": 0,
                "error": str(e)
            }
            
    async def run_concurrent_test(self, num_requests: int = 50) -> Dict[str, Any]:
        """Run concurrent test with specified number of requests"""
        logger.info(f"Starting concurrent test with {num_requests} requests")
        
        # Check if sentiment.json exists
        json_file_path = "sentiment.json"
        if not os.path.exists(json_file_path):
            raise FileNotFoundError(f"sentiment.json file not found in current directory")
        
        try:
            # Record start time
            test_start_time = time.time()
            
            # Create tasks for concurrent requests
            tasks = [
                self.make_single_request(i + 1, json_file_path)
                for i in range(num_requests)
            ]
            
            # Execute all requests concurrently
            logger.info("Executing concurrent requests...")
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Calculate total test duration
            total_duration = time.time() - test_start_time
            
            # Process results
            successful_requests = 0
            failed_requests = 0
            total_request_duration = 0
            min_duration = float('inf')
            max_duration = 0
            status_codes = {}
            errors = {}
            
            for result in results:
                if isinstance(result, Exception):
                    failed_requests += 1
                    error_msg = str(result)
                    errors[error_msg] = errors.get(error_msg, 0) + 1
                else:
                    if result["success"]:
                        successful_requests += 1
                    else:
                        failed_requests += 1
                        
                    duration = result["duration"]
                    total_request_duration += duration
                    min_duration = min(min_duration, duration)
                    max_duration = max(max_duration, duration)
                    
                    status_code = result["status_code"]
                    status_codes[status_code] = status_codes.get(status_code, 0) + 1
                    
                    if "error" in result:
                        error_msg = result["error"]
                        errors[error_msg] = errors.get(error_msg, 0) + 1
            
            # Calculate statistics
            success_rate = (successful_requests / num_requests) * 100
            avg_duration = total_request_duration / num_requests if num_requests > 0 else 0
            requests_per_second = num_requests / total_duration if total_duration > 0 else 0
            
            # Compile analysis
            analysis = {
                "test_summary": {
                    "total_requests": num_requests,
                    "successful_requests": successful_requests,
                    "failed_requests": failed_requests,
                    "success_rate": success_rate,
                    "total_test_duration": total_duration
                },
                "timing_stats": {
                    "average_request_duration": avg_duration,
                    "min_request_duration": min_duration if min_duration != float('inf') else 0,
                    "max_request_duration": max_duration,
                    "requests_per_second": requests_per_second
                },
                "status_code_distribution": status_codes,
                "error_analysis": errors,
                "queue_performance": {
                    "concurrent_capacity": num_requests,
                    "queue_handling": "PASSED" if success_rate > 80 else "FAILED",
                    "timeout_rate": 0.0
                }
            }
            
            return {
                "analysis": analysis,
                "detailed_results": [r for r in results if not isinstance(r, Exception)]
            }
            
        except Exception as e:
            logger.error(f"Test failed: {str(e)}")
            raise
                
    def save_results(self, results: Dict[str, Any], filename: str = None):
        """Save test results to JSON file"""
        if filename is None:
            timestamp = int(time.time())
            filename = f"generic_entity_concurrent_test_results_{timestamp}.json"
            
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
            
        logger.info(f"Results saved to {filename}")
        return filename

async def main():
    """Main function to run the concurrent test"""
    # Configuration
    BASE_URL = "http://127.0.0.1:8000"
    TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjgyYzJiNGQwZWIwOTkxN2U1ZDkzOTU0IiwiZXhwIjoxNzU0NDcyMTU4fQ.OpBLLih8r6f8A9N7PuvGQMvSyrLnU6H-mtzkpWEZ2NE"
    NUM_REQUESTS = 50
    
    logger.info("🔍 Generic Entity Extraction Concurrent Test")
    logger.info("=" * 60)
    
    async with GenericEntityConcurrentTester(BASE_URL, TOKEN) as tester:
        logger.info(f"Starting concurrent test with {NUM_REQUESTS} requests")
        logger.info(f"Target URL: {BASE_URL}/v1/processes/generic-entity-extraction/run")
        logger.info(f"Using file: sentiment.json")
        logger.info("🚀 Starting requests...")
        
        # Run the test
        results = await tester.run_concurrent_test(NUM_REQUESTS)
        
        # Save results
        filename = tester.save_results(results)
        
        # Print summary
        analysis = results["analysis"]
        print("\n" + "="*60)
        print("CONCURRENT GENERIC ENTITY EXTRACTION TEST RESULTS")
        print("="*60)
        print(f"Total Requests: {analysis['test_summary']['total_requests']}")
        print(f"Successful: {analysis['test_summary']['successful_requests']}")
        print(f"Failed: {analysis['test_summary']['failed_requests']}")
        print(f"Success Rate: {analysis['test_summary']['success_rate']:.1f}%")
        print(f"Total Duration: {analysis['test_summary']['total_test_duration']:.2f}s")
        print(f"Avg Request Duration: {analysis['timing_stats']['average_request_duration']:.2f}s")
        print(f"Requests/Second: {analysis['timing_stats']['requests_per_second']:.2f}")
        print(f"Queue Performance: {analysis['queue_performance']['queue_handling']}")
        
        print(f"\nStatus Code Distribution:")
        for code, count in analysis['status_code_distribution'].items():
            print(f"  {code}: {count}")
            
        if analysis['error_analysis']:
            print(f"\nError Analysis:")
            for error, count in analysis['error_analysis'].items():
                print(f"  {error}: {count}")
        
        print(f"\nResults saved to: {filename}")
        print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
