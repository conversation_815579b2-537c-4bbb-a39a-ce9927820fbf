#!/usr/bin/env python3
"""
Test script to run 100 concurrent requests to the audio-transcribe-analysis API
to test queue settings and concurrency handling.

This script will:
1. Set the test mode environment variable
2. Run 100 concurrent requests to the API
3. Monitor response times and success rates
4. Test the queue system's ability to handle concurrent load
"""

import asyncio
import aiohttp
import time
import os
import sys
from typing import List, Dict, Any
import json
from pathlib import Path

# Configuration
API_BASE_URL = "http://127.0.0.1:8000"
API_ENDPOINT = "/v1/processes/audio-transcribe-analysis/run"
AUTH_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjgyYzJiNGQwZWIwOTkxN2U1ZDkzOTU0IiwiZXhwIjoxNzU0NDU5ODM5fQ.knE3lhewshG4C7VyDgpjPtRHTyjZwMol720Uu1PKT1Y"
AUDIO_FILE_PATH = "Record (online-voice-recorder.com) (1).mp3"
CONCURRENT_REQUESTS = 100
TIMEOUT_SECONDS = 300

class ConcurrentTestRunner:
    def __init__(self):
        self.results: List[Dict[str, Any]] = []
        self.start_time = None
        self.end_time = None
        
    async def make_single_request(self, session: aiohttp.ClientSession, request_id: int) -> Dict[str, Any]:
        """Make a single API request and return the result with timing info."""
        request_start = time.time()
        
        try:
            # Check if audio file exists
            if not os.path.exists(AUDIO_FILE_PATH):
                return {
                    "request_id": request_id,
                    "success": False,
                    "error": f"Audio file not found: {AUDIO_FILE_PATH}",
                    "duration": 0,
                    "status_code": None
                }
            
            # Prepare the multipart form data
            data = aiohttp.FormData()
            data.add_field('files', 
                          open(AUDIO_FILE_PATH, 'rb'), 
                          filename=AUDIO_FILE_PATH,
                          content_type='audio/mpeg')
            
            # Make the request
            url = f"{API_BASE_URL}{API_ENDPOINT}?wait_for_completion=true&timeout={TIMEOUT_SECONDS}"
            headers = {
                'accept': 'application/json',
                'Authorization': f'Bearer {AUTH_TOKEN}'
            }
            
            async with session.post(url, data=data, headers=headers) as response:
                request_end = time.time()
                duration = request_end - request_start
                
                response_text = await response.text()
                
                result = {
                    "request_id": request_id,
                    "success": response.status == 200,
                    "status_code": response.status,
                    "duration": duration,
                    "response_size": len(response_text)
                }
                
                if response.status == 200:
                    try:
                        response_json = json.loads(response_text)
                        result["job_id"] = response_json.get("job_id")
                        result["status"] = response_json.get("status")
                    except json.JSONDecodeError:
                        result["error"] = "Failed to parse JSON response"
                else:
                    result["error"] = f"HTTP {response.status}: {response_text[:200]}"
                
                return result
                
        except asyncio.TimeoutError:
            return {
                "request_id": request_id,
                "success": False,
                "error": "Request timeout",
                "duration": time.time() - request_start,
                "status_code": None
            }
        except Exception as e:
            return {
                "request_id": request_id,
                "success": False,
                "error": str(e),
                "duration": time.time() - request_start,
                "status_code": None
            }
    
    async def run_concurrent_test(self) -> Dict[str, Any]:
        """Run the concurrent test with specified number of requests."""
        print(f"Starting concurrent test with {CONCURRENT_REQUESTS} requests...")
        print(f"API Endpoint: {API_BASE_URL}{API_ENDPOINT}")
        print(f"Audio file: {AUDIO_FILE_PATH}")
        print(f"Timeout: {TIMEOUT_SECONDS} seconds")
        print("-" * 60)
        
        self.start_time = time.time()
        
        # Create aiohttp session with timeout
        timeout = aiohttp.ClientTimeout(total=TIMEOUT_SECONDS + 30)  # Add buffer
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # Create tasks for all requests
            tasks = [
                self.make_single_request(session, i) 
                for i in range(1, CONCURRENT_REQUESTS + 1)
            ]
            
            # Run all requests concurrently
            print(f"Launching {len(tasks)} concurrent requests...")
            self.results = await asyncio.gather(*tasks, return_exceptions=True)
            
        self.end_time = time.time()
        
        # Process results and handle exceptions
        processed_results = []
        for i, result in enumerate(self.results):
            if isinstance(result, Exception):
                processed_results.append({
                    "request_id": i + 1,
                    "success": False,
                    "error": str(result),
                    "duration": 0,
                    "status_code": None
                })
            else:
                processed_results.append(result)
        
        self.results = processed_results
        return self.analyze_results()
    
    def analyze_results(self) -> Dict[str, Any]:
        """Analyze the test results and return summary statistics."""
        total_duration = self.end_time - self.start_time
        successful_requests = [r for r in self.results if r["success"]]
        failed_requests = [r for r in self.results if not r["success"]]
        
        # Calculate timing statistics
        durations = [r["duration"] for r in self.results if r["duration"] > 0]
        avg_duration = sum(durations) / len(durations) if durations else 0
        min_duration = min(durations) if durations else 0
        max_duration = max(durations) if durations else 0
        
        # Status code distribution
        status_codes = {}
        for result in self.results:
            code = result.get("status_code", "None")
            status_codes[code] = status_codes.get(code, 0) + 1
        
        # Error analysis
        error_types = {}
        for result in failed_requests:
            error = result.get("error", "Unknown error")
            error_types[error] = error_types.get(error, 0) + 1
        
        analysis = {
            "test_summary": {
                "total_requests": len(self.results),
                "successful_requests": len(successful_requests),
                "failed_requests": len(failed_requests),
                "success_rate": len(successful_requests) / len(self.results) * 100,
                "total_test_duration": total_duration
            },
            "timing_stats": {
                "average_request_duration": avg_duration,
                "min_request_duration": min_duration,
                "max_request_duration": max_duration,
                "requests_per_second": len(self.results) / total_duration if total_duration > 0 else 0
            },
            "status_code_distribution": status_codes,
            "error_analysis": error_types,
            "queue_performance": {
                "concurrent_capacity": CONCURRENT_REQUESTS,
                "queue_handling": "PASSED" if len(successful_requests) > 0 else "FAILED",
                "timeout_rate": len([r for r in failed_requests if "timeout" in r.get("error", "").lower()]) / len(self.results) * 100
            }
        }
        
        return analysis

def print_results(analysis: Dict[str, Any]):
    """Print formatted test results."""
    print("\n" + "="*60)
    print("CONCURRENT API TEST RESULTS")
    print("="*60)
    
    summary = analysis["test_summary"]
    print(f"Total Requests: {summary['total_requests']}")
    print(f"Successful: {summary['successful_requests']}")
    print(f"Failed: {summary['failed_requests']}")
    print(f"Success Rate: {summary['success_rate']:.2f}%")
    print(f"Total Test Duration: {summary['total_test_duration']:.2f} seconds")
    
    print("\nTiming Statistics:")
    timing = analysis["timing_stats"]
    print(f"  Average Request Duration: {timing['average_request_duration']:.2f} seconds")
    print(f"  Min Request Duration: {timing['min_request_duration']:.2f} seconds")
    print(f"  Max Request Duration: {timing['max_request_duration']:.2f} seconds")
    print(f"  Requests Per Second: {timing['requests_per_second']:.2f}")
    
    print("\nStatus Code Distribution:")
    for code, count in analysis["status_code_distribution"].items():
        print(f"  {code}: {count}")
    
    if analysis["error_analysis"]:
        print("\nError Analysis:")
        for error, count in analysis["error_analysis"].items():
            print(f"  {error}: {count}")
    
    print("\nQueue Performance:")
    queue = analysis["queue_performance"]
    print(f"  Concurrent Capacity Test: {queue['concurrent_capacity']} requests")
    print(f"  Queue Handling: {queue['queue_handling']}")
    print(f"  Timeout Rate: {queue['timeout_rate']:.2f}%")

async def main():
    """Main function to run the concurrent test."""
    print("Audio Processing Concurrent Test")
    print("="*60)
    
    # Check if audio file exists
    if not os.path.exists(AUDIO_FILE_PATH):
        print(f"ERROR: Audio file not found: {AUDIO_FILE_PATH}")
        print("Please ensure the audio file exists in the current directory.")
        sys.exit(1)
    
    # Set test mode environment variable
    os.environ["AUDIO_ANALYSIS_TEST_MODE"] = "true"
    print("✓ Test mode enabled (10-second sleep instead of actual processing)")
    
    # Run the test
    runner = ConcurrentTestRunner()
    analysis = await runner.run_concurrent_test()
    
    # Print results
    print_results(analysis)
    
    # Save detailed results to file
    timestamp = int(time.time())
    results_file = f"concurrent_test_results_{timestamp}.json"
    with open(results_file, 'w') as f:
        json.dump({
            "analysis": analysis,
            "detailed_results": runner.results
        }, f, indent=2)
    
    print(f"\nDetailed results saved to: {results_file}")

if __name__ == "__main__":
    asyncio.run(main())
