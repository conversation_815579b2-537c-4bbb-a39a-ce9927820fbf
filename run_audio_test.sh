#!/bin/bash

# Audio Analysis Concurrent Test Runner
# This script sets up the test environment and runs the concurrent test

echo "🎵 Audio Analysis Concurrent Test Runner"
echo "========================================"

# Set test mode to enable mock processing (10-second sleep instead of LLM)
export AUDIO_ANALYSIS_TEST_MODE=true

echo "✅ Test mode enabled (AUDIO_ANALYSIS_TEST_MODE=true)"
echo "📝 This will use 10-second sleep instead of actual LLM processing"
echo "🚀 Starting 50 concurrent requests test..."
echo ""

# Install required dependencies if not present
if ! python -c "import aiohttp" 2>/dev/null; then
    echo "📦 Installing aiohttp..."
    pip install aiohttp
fi

# Run the test
python test_audio_concurrent.py

echo ""
echo "✅ Test completed!"
echo "📊 Check the generated JSON file for detailed results"
