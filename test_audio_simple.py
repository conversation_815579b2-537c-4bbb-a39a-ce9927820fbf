#!/usr/bin/env python3
"""
Simple Audio Analysis API Test Script

This script tests the audio-transcribe-analysis API endpoint with a few requests first
to ensure everything is working before running the full concurrent test.
"""

import asyncio
import aiohttp
import time
import json
import os
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_single_request():
    """Test a single request to the API"""
    
    # Set test mode
    os.environ["AUDIO_ANALYSIS_TEST_MODE"] = "true"
    
    # Configuration
    BASE_URL = "http://127.0.0.1:8300"
    TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjgyYzJiNGQwZWIwOTkxN2U1ZDkzOTU0IiwiZXhwIjoxNzU0NDcyMTU4fQ.OpBLLih8r6f8A9N7PuvGQMvSyrLnU6H-mtzkpWEZ2NE"
    
    # Create test audio file
    test_audio_path = "test_audio.mp3"
    mp3_header = b'ID3\x03\x00\x00\x00\x00\x00\x00'
    mp3_frame = b'\xff\xfb\x90\x00' + b'\x00' * 100
    
    with open(test_audio_path, 'wb') as f:
        f.write(mp3_header + mp3_frame)
    
    try:
        async with aiohttp.ClientSession() as session:
            url = f"{BASE_URL}/v1/processes/audio-transcribe-analysis/run"
            params = {
                'wait_for_completion': 'true',
                'timeout': '300'
            }
            headers = {
                'accept': 'application/json',
                'Authorization': f'Bearer {TOKEN}'
            }
            
            logger.info("Testing single request...")
            start_time = time.time()
            
            with open(test_audio_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('files', f, filename='test_audio.mp3', content_type='audio/mpeg')
                
                async with session.post(url, headers=headers, params=params, data=data) as response:
                    duration = time.time() - start_time
                    response_text = await response.text()
                    
                    logger.info(f"Response status: {response.status}")
                    logger.info(f"Response time: {duration:.2f}s")
                    logger.info(f"Response size: {len(response_text)} bytes")
                    
                    if response.status == 200:
                        try:
                            response_json = json.loads(response_text)
                            logger.info(f"Success! Job ID: {response_json.get('job_id')}")
                            logger.info(f"Status: {response_json.get('status')}")
                            return True
                        except json.JSONDecodeError:
                            logger.error("Invalid JSON response")
                            logger.error(f"Response: {response_text[:500]}")
                            return False
                    else:
                        logger.error(f"Request failed: {response.status}")
                        logger.error(f"Response: {response_text[:500]}")
                        return False
                        
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return False
    finally:
        if os.path.exists(test_audio_path):
            os.remove(test_audio_path)

async def test_concurrent_requests(num_requests=5):
    """Test multiple concurrent requests"""
    
    # Set test mode
    os.environ["AUDIO_ANALYSIS_TEST_MODE"] = "true"
    
    # Configuration
    BASE_URL = "http://127.0.0.1:8300"
    TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjgyYzJiNGQwZWIwOTkxN2U1ZDkzOTU0IiwiZXhwIjoxNzU0NDcyMTU4fQ.OpBLLih8r6f8A9N7PuvGQMvSyrLnU6H-mtzkpWEZ2NE"
    
    # Create test audio file
    test_audio_path = "test_audio.mp3"
    mp3_header = b'ID3\x03\x00\x00\x00\x00\x00\x00'
    mp3_frame = b'\xff\xfb\x90\x00' + b'\x00' * 100
    
    with open(test_audio_path, 'wb') as f:
        f.write(mp3_header + mp3_frame)
    
    async def make_request(request_id):
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{BASE_URL}/v1/processes/audio-transcribe-analysis/run"
                params = {
                    'wait_for_completion': 'true',
                    'timeout': '300'
                }
                headers = {
                    'accept': 'application/json',
                    'Authorization': f'Bearer {TOKEN}'
                }
                
                start_time = time.time()
                
                with open(test_audio_path, 'rb') as f:
                    data = aiohttp.FormData()
                    data.add_field('files', f, filename='test_audio.mp3', content_type='audio/mpeg')
                    
                    async with session.post(url, headers=headers, params=params, data=data) as response:
                        duration = time.time() - start_time
                        response_text = await response.text()
                        
                        result = {
                            "request_id": request_id,
                            "success": response.status == 200,
                            "status_code": response.status,
                            "duration": duration,
                            "response_size": len(response_text)
                        }
                        
                        if response.status == 200:
                            try:
                                response_json = json.loads(response_text)
                                result["job_id"] = response_json.get("job_id")
                                result["status"] = response_json.get("status")
                            except json.JSONDecodeError:
                                result["error"] = "Invalid JSON response"
                        else:
                            result["error"] = f"HTTP {response.status}: {response_text[:100]}"
                            
                        return result
                        
        except Exception as e:
            return {
                "request_id": request_id,
                "success": False,
                "error": str(e)
            }
    
    try:
        logger.info(f"Testing {num_requests} concurrent requests...")
        start_time = time.time()
        
        # Create tasks
        tasks = [make_request(i + 1) for i in range(num_requests)]
        
        # Execute concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_duration = time.time() - start_time
        
        # Analyze results
        successful = sum(1 for r in results if isinstance(r, dict) and r.get("success"))
        failed = len(results) - successful
        
        logger.info(f"Results: {successful}/{len(results)} successful")
        logger.info(f"Total time: {total_duration:.2f}s")
        logger.info(f"Success rate: {(successful/len(results)*100):.1f}%")
        
        # Print individual results
        for result in results:
            if isinstance(result, dict):
                status = "✅" if result.get("success") else "❌"
                logger.info(f"{status} Request {result.get('request_id')}: {result.get('duration', 0):.2f}s")
                if not result.get("success"):
                    logger.error(f"   Error: {result.get('error', 'Unknown error')}")
        
        return successful == len(results)
        
    finally:
        if os.path.exists(test_audio_path):
            os.remove(test_audio_path)

async def main():
    """Main function"""
    logger.info("🎵 Audio Analysis API Test")
    logger.info("=" * 40)
    
    # Test 1: Single request
    logger.info("Test 1: Single Request")
    success1 = await test_single_request()
    
    if success1:
        logger.info("✅ Single request test passed!")
        
        # Test 2: Small concurrent test
        logger.info("\nTest 2: Small Concurrent Test (5 requests)")
        success2 = await test_concurrent_requests(5)
        
        if success2:
            logger.info("✅ Small concurrent test passed!")
            logger.info("\n🚀 Ready for full 50-request test!")
        else:
            logger.error("❌ Small concurrent test failed")
    else:
        logger.error("❌ Single request test failed")

if __name__ == "__main__":
    asyncio.run(main())
